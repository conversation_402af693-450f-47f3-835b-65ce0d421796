﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.RoomTypes;

[Authorize(WismaAppPermissions.PolicyRoomType.Default)]
public class RoomTypeAppService : PermissionCheckedCrudAppService<
        RoomType,
        RoomTypeDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateRoomTypeDto,
        CreateUpdateRoomTypeDto
    >, IRoomTypeAppService
{
    private readonly IRepository<RoomType, Guid> _repository;

    public RoomTypeAppService(IRepository<RoomType, Guid> repository, IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _repository = repository;

        GetPolicyName = WismaAppPermissions.PolicyRoomType.View;
        GetListPolicyName = WismaAppPermissions.PolicyRoomType.View;
        CreatePolicyName = WismaAppPermissions.PolicyRoomType.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyRoomType.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyRoomType.Delete;
    }

    protected override async Task<IQueryable<RoomType>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.Status);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyRoomType.View)]
    public override async Task<RoomTypeDto> GetAsync(Guid id)
    {
        // await CheckGetPolicyAsync();
        var query = await Repository.GetQueryableAsync();
        var roomType = await query
            .Include(x => x.Status)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (roomType is null)
        {
            throw new EntityNotFoundException(typeof(RoomType), id);
        }

        return ObjectMapper.Map<RoomType, RoomTypeDto>(roomType);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyRoomType.Create)]
    public override async Task<RoomTypeDto> CreateAsync(CreateUpdateRoomTypeDto input)
    {
        var entity = new RoomType(
            GuidGenerator.Create(),
            input.Name,
            input.StatusId,
            input.Alias
        );

        await _repository.InsertAsync(entity);
        return ObjectMapper.Map<RoomType, RoomTypeDto>(entity);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyRoomType.Edit)]
    public override async Task<RoomTypeDto> UpdateAsync(Guid id, CreateUpdateRoomTypeDto input)
    {
        var entity = await _repository.GetAsync(id);

        entity.Name = input.Name;
        entity.StatusId = input.StatusId;
        entity.Alias = input.Alias;

        await _repository.UpdateAsync(entity);
        return ObjectMapper.Map<RoomType, RoomTypeDto>(entity);
    }
}