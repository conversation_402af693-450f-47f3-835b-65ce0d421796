using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Services;

[Authorize(WismaAppPermissions.PolicyService.Default)]
public class ServicesAppService : PermissionCheckedCrudAppService<
        Service,
        ServicesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateServicesDto,
        CreateUpdateServicesDto
    >, IServicesAppService
{
    private readonly IRepository<Service, Guid> _repository;
    private readonly ILogger<ServicesAppService> _logger;

    public ServicesAppService(
        IRepository<Service, Guid> repository,
        ILogger<ServicesAppService> logger,
        IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _repository = repository;
        _logger = logger;

        GetPolicyName = WismaAppPermissions.PolicyService.View;
        GetListPolicyName = WismaAppPermissions.PolicyService.View;
        CreatePolicyName = WismaAppPermissions.PolicyService.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyService.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyService.Delete;
    }

    protected override async Task<IQueryable<Service>> CreateFilteredQueryAsync(PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.ServiceType);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyService.View)]
    public override async Task<ServicesDto> GetAsync(Guid id)
    {
        var query = await Repository.GetQueryableAsync();
        var service = await query
            .Include(x => x.ServiceType)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (service == null)
        {
            throw new EntityNotFoundException(typeof(Service), id);
        }

        return ObjectMapper.Map<Service, ServicesDto>(service);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyService.Create)]
    public override async Task<ServicesDto> CreateAsync(CreateUpdateServicesDto input)
    {
        // await CheckCreatePolicyAsync();

        var entity = await MapToEntityAsync(input);

        // Set ExtraProperties for foreign names if provided in the input
        if (input.ExtraProperties != null)
        {
            foreach (var property in input.ExtraProperties)
            {
                entity.SetProperty(property.Key, property.Value);
            }
        }
        else
        {
            // Set default foreign names if not provided
            entity.SetProperty("ForeignName1", string.Empty);
            entity.SetProperty("ForeignName2", string.Empty);
        }

        await Repository.InsertAsync(entity);

        return await MapToGetOutputDtoAsync(entity);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyService.Edit)]
    public override async Task<ServicesDto> UpdateAsync(Guid id, CreateUpdateServicesDto input)
    {
        // await CheckUpdatePolicyAsync();

        var entity = await Repository.GetAsync(id);

        await MapToEntityAsync(input, entity);

        // Update ExtraProperties for foreign names if provided in the input
        if (input.ExtraProperties != null)
        {
            foreach (var property in input.ExtraProperties)
            {
                entity.SetProperty(property.Key, property.Value);
            }
        }

        await Repository.UpdateAsync(entity);

        return await MapToGetOutputDtoAsync(entity);
    }
}