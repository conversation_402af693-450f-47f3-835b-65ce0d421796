using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Validation;

namespace Imip.HotelFrontOffice.PaymentDetails;

[Authorize(WismaAppPermissions.PolicyPaymentDetails.Default)]
public class PaymentDetailsAppService : CrudAppService<
    PaymentDetail,
    PaymentDetailsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdatePaymentDetailsDto,
    CreateUpdatePaymentDetailsDto
>, IPaymentDetailsAppService
{
    private readonly IRepository<PaymentDetail, Guid> _repository;

    public PaymentDetailsAppService(IRepository<PaymentDetail, Guid> repository,
        ILogger<PaymentDetailsAppService> logger)
        : base(repository)
    {
        _repository = repository;
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyPaymentDetails.View)]
    public override Task<PagedResultDto<PaymentDetailsDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyPaymentDetails.View)]
    public override Task<PaymentDetailsDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyPaymentDetails.Create)]
    public override async Task<PaymentDetailsDto> CreateAsync(CreateUpdatePaymentDetailsDto input)
    {
        // await CheckCreatePolicyAsync();

        // Validate that the SourceType is a valid enum value
        // This check allows both enum values (1, 2, 3) and enum names ("ReservationRoom", etc.)
        if (!Enum.IsDefined(typeof(PaymentSourceType), input.SourceType))
        {
            // Try to convert numeric value to enum if it's not a valid enum directly
            // This handles cases where the client sends numeric values (1, 2, 3) instead of enum names
            int sourceTypeValue;
            bool isValidSourceType = false;

            // Check if the value is a valid integer that corresponds to an enum value
            if (int.TryParse(input.SourceType.ToString(), out sourceTypeValue))
            {
                isValidSourceType = Enum.IsDefined(typeof(PaymentSourceType), sourceTypeValue);
                if (isValidSourceType)
                {
                    // Convert the numeric value to the actual enum value
                    input.SourceType = (PaymentSourceType)sourceTypeValue;
                }
            }

            if (!isValidSourceType)
            {
                throw new AbpValidationException(
                    "Invalid source type. Must be one of the valid payment source types (1=ReservationRoom, 2=ReservationRoomFoodAndBeverage, 3=ReservationRoomService).",
                    new ValidationResult[]
                    {
                        new ValidationResult(
                            "Invalid source type. Must be one of the valid payment source types (1=ReservationRoom, 2=ReservationRoomFoodAndBeverage, 3=ReservationRoomService).",
                            new[] { nameof(input.SourceType) }
                        )
                    });
            }
        }

        var entity = await MapToEntityAsync(input);

        await Repository.InsertAsync(entity, autoSave: true);

        return await MapToGetOutputDtoAsync(entity);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyPaymentDetails.Edit)]
    public override async Task<PaymentDetailsDto> UpdateAsync(Guid id, CreateUpdatePaymentDetailsDto input)
    {
        // await CheckUpdatePolicyAsync();

        // Validate that the SourceType is a valid enum value
        // This check allows both enum values (1, 2, 3) and enum names ("ReservationRoom", etc.)
        if (!Enum.IsDefined(typeof(PaymentSourceType), input.SourceType))
        {
            // Try to convert numeric value to enum if it's not a valid enum directly
            // This handles cases where the client sends numeric values (1, 2, 3) instead of enum names
            int sourceTypeValue;
            bool isValidSourceType = false;

            // Check if the value is a valid integer that corresponds to an enum value
            if (int.TryParse(input.SourceType.ToString(), out sourceTypeValue))
            {
                isValidSourceType = Enum.IsDefined(typeof(PaymentSourceType), sourceTypeValue);
                if (isValidSourceType)
                {
                    // Convert the numeric value to the actual enum value
                    input.SourceType = (PaymentSourceType)sourceTypeValue;
                }
            }

            if (!isValidSourceType)
            {
                throw new AbpValidationException(
                    "Invalid source type. Must be one of the valid payment source types (1=ReservationRoom, 2=ReservationRoomFoodAndBeverage, 3=ReservationRoomService).",
                    new ValidationResult[]
                    {
                        new ValidationResult(
                            "Invalid source type. Must be one of the valid payment source types (1=ReservationRoom, 2=ReservationRoomFoodAndBeverage, 3=ReservationRoomService).",
                            new[] { nameof(input.SourceType) }
                        )
                    });
            }
        }

        var entity = await GetEntityByIdAsync(id);

        await MapToEntityAsync(input, entity);

        await Repository.UpdateAsync(entity, autoSave: true);

        return await MapToGetOutputDtoAsync(entity);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyPaymentDetails.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}