﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.RoomTypes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.ServiceTypes;

public class ServiceTypesAppService : PermissionCheckedCrudAppService<
        ServiceType,
        ServiceTypesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateServiceTypesDto,
        CreateUpdateServiceTypesDto
    >, IServiceTypesAppService
{
    private readonly IRepository<ServiceType, Guid> _repository;
    public ServiceTypesAppService(IRepository<ServiceType, Guid> repository, IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _repository = repository;

        GetPolicyName = WismaAppPermissions.PolicyServiceType.View;
        GetListPolicyName = WismaAppPermissions.PolicyServiceType.View;
        CreatePolicyName = WismaAppPermissions.PolicyServiceType.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyServiceType.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyServiceType.Delete;
    }

    protected override async Task<IQueryable<ServiceType>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.Status);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyServiceType.View)]
    public override async Task<ServiceTypesDto> GetAsync(Guid id)
    {
        // await CheckGetPolicyAsync();

        var query = await Repository.GetQueryableAsync();
        var serviceType = await query
            .Include(x => x.Status)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (serviceType is null)
        {
            throw new EntityNotFoundException(typeof(ServiceType), id);
        }

        return ObjectMapper.Map<ServiceType, ServiceTypesDto>(serviceType);
    }
}