using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Validation;

namespace Imip.HotelFrontOffice.ReservationRooms;

[Authorize(WismaAppPermissions.PolicyReservationRoom.Default)]
public class ReservationRoomsAppService : IdentityServerCrudAppService<
        ReservationRoom,
        ReservationRoomsDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateReservationRoomsDto,
        CreateUpdateReservationRoomsDto
    >, IReservationRoomsAppService
{
    private readonly IRepository<ReservationRoom, Guid> _repository;

    public ReservationRoomsAppService(IRepository<ReservationRoom, Guid> repository, ILogger<ReservationRoomsAppService> logger)
        : base(repository, logger)
    {
        _repository = repository;
    }

    protected override async Task<IQueryable<ReservationRoom>> CreateFilteredQueryAsync(PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.ReservationDetails)
            .Include(x => x.Services);  // Changed from Services to Service
    }

    public override async Task<ReservationRoomsDto> GetAsync(Guid id)
    {
        var query = await Repository.GetQueryableAsync();
        var reservationRoom = await query
            .Include(x => x.ReservationDetails)
            .Include(x => x.Services)  // Changed from Services to Service
            .Include(x => x.PaymentStatus)  // Added for payment status navigation property
            .FirstOrDefaultAsync(x => x.Id == id);

        if (reservationRoom == null)
        {
            throw new EntityNotFoundException(typeof(ReservationRoom), id);
        }
        return ObjectMapper.Map<ReservationRoom, ReservationRoomsDto>(reservationRoom);
    }

    [Authorize(WismaAppPermissions.PolicyReservationRoom.Create)]
    public async Task<ReservationRoomsDto> CreateManyAsync([FromBody] List<CreateUpdateReservationRoomsDto> items)
    {
        // await CheckCreatePolicyAsync();

        // Validate inputs
        if (items == null || items.Count == 0)
        {
            throw new AbpValidationException("No items provided for creation");
        }

        var entitiesToInsert = new List<ReservationRoom>();
        var entitiesToUpdate = new List<ReservationRoom>();
        var allEntities = new List<ReservationRoom>();

        foreach (var item in items)
        {
            // Validate each item
            if (item.ReservationDetailsId == Guid.Empty)
            {
                throw new AbpValidationException("ReservationDetailsId is required");
            }

            if (item.ServiceId == Guid.Empty)
            {
                throw new AbpValidationException("ServiceId is required");
            }

            if (item.Quantity <= 0)
            {
                throw new AbpValidationException("Quantity must be greater than 0");
            }

            if (item.TotalPrice < 0)
            {
                throw new AbpValidationException("TotalPrice must be greater than or equal to 0");
            }

            // Check if the item has an ID and if it exists in the database
            if (item.Id != Guid.Empty)
            {
                try
                {
                    // Try to get the existing entity
                    var existingEntity = await GetEntityByIdAsync(item.Id);

                    // Update the existing entity
                    await MapToEntityAsync(item, existingEntity);
                    entitiesToUpdate.Add(existingEntity);
                    allEntities.Add(existingEntity);
                }
                catch (EntityNotFoundException)
                {
                    // If entity with the provided ID doesn't exist, create a new one
                    var newEntity = await MapToEntityAsync(item);
                    entitiesToInsert.Add(newEntity);
                    allEntities.Add(newEntity);
                }
            }
            else
            {
                // Create a new entity if no ID is provided
                var newEntity = await MapToEntityAsync(item);
                entitiesToInsert.Add(newEntity);
                allEntities.Add(newEntity);
            }
        }

        // Insert new entities
        if (entitiesToInsert.Count > 0)
        {
            await Repository.InsertManyAsync(entitiesToInsert, autoSave: true);
        }

        // Update existing entities
        if (entitiesToUpdate.Count > 0)
        {
            await Repository.UpdateManyAsync(entitiesToUpdate, autoSave: true);
        }

        // Return the first item as a sample of what was created/updated
        var firstEntity = allEntities.FirstOrDefault();
        if (firstEntity == null)
        {
            // Return an empty DTO if no entities were created/updated
            return new ReservationRoomsDto();
        }

        return await MapToGetOutputDtoAsync(firstEntity);
    }
}