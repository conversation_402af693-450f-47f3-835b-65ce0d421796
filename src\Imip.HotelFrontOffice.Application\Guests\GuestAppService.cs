using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Guests;

[Authorize(WismaAppPermissions.PolicyGuest.Default)]
public class GuestAppService : IdentityServerCrudAppService<
    Guest,
    GuestDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateGuestDto,
    CreateUpdateGuestDto
>, IGuestAppService
{
    private readonly IAttachmentAppService _attachmentAppService;
    private readonly ILogger<GuestAppService> _logger;

    public GuestAppService(
        IRepository<Guest, Guid> repository,
        IAttachmentAppService attachmentAppService,
        ILogger<GuestAppService> logger)
        : base(repository, logger)
    {
        _attachmentAppService = attachmentAppService;
        _logger = logger;
    }

    protected override async Task<IQueryable<Guest>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input));
    }

    [Authorize(WismaAppPermissions.PolicyGuest.View)]
    public override async Task<GuestDto> GetAsync(Guid id)
    {
        var query = await Repository.GetQueryableAsync();
        var guest = await query
            .FirstOrDefaultAsync(x => x.Id == id);

        if (guest == null)
        {
            throw new EntityNotFoundException(typeof(Guest), id);
        }

        var guestDto = ObjectMapper.Map<Guest, GuestDto>(guest);

        // Load attachments for the guest
        try
        {
            var attachments = await _attachmentAppService.GetByReferenceAsync(id, "Guest");
            if (attachments != null && attachments.Count > 0)
            {
                guestDto.Attachments = attachments.Select(a => new GuestAttachmentInfoDto
                {
                    Id = a.Id,
                    FileName = a.FileName,
                    ContentType = a.ContentType,
                    Size = a.Size,
                    Url = a.Url,
                    StreamUrl = a.StreamUrl,
                    Description = null, // FileUploadResultDto doesn't have Description
                    CreationTime = a.UploadTime
                }).ToList();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to load attachments for guest {GuestId}: {Message}", id, ex.Message);
            // Continue without attachments rather than failing the entire request
        }

        return guestDto;
    }

    [Authorize(WismaAppPermissions.PolicyGuest.Create)]
    public override async Task<GuestDto> CreateAsync(CreateUpdateGuestDto input)
    {
        // Create the guest first
        var guestDto = await base.CreateAsync(input);

        // Process attachments if provided
        if (input.Attachments != null && input.Attachments.Count > 0)
        {
            await ProcessGuestAttachmentsAsync(input.Attachments, guestDto.Id, guestDto.Fullname);
        }

        // Return the guest with attachments loaded
        return await GetAsync(guestDto.Id);
    }

    [Authorize(WismaAppPermissions.PolicyGuest.Edit)]
    public override async Task<GuestDto> UpdateAsync(Guid id, CreateUpdateGuestDto input)
    {
        // Update the guest first
        var guestDto = await base.UpdateAsync(id, input);

        // Process attachments if provided
        if (input.Attachments != null && input.Attachments.Count > 0)
        {
            await ProcessGuestAttachmentsAsync(input.Attachments, id, guestDto.Fullname);
        }

        // Return the guest with attachments loaded
        return await GetAsync(id);
    }

    /// <summary>
    /// Upload attachments for a guest
    /// </summary>
    public async Task UploadAttachmentsAsync(Guid guestId, List<GuestAttachmentDto> attachments)
    {
        var guest = await Repository.GetAsync(guestId);
        await ProcessGuestAttachmentsAsync(attachments, guestId, guest.Fullname ?? "Unknown Guest");
    }

    /// <summary>
    /// Process guest attachments and upload them to the SFTP server
    /// </summary>
    private async Task ProcessGuestAttachmentsAsync(List<GuestAttachmentDto> attachments, Guid guestId, string guestName)
    {
        foreach (var attachment in attachments)
        {
            try
            {
                // Validate the base64 string
                if (string.IsNullOrEmpty(attachment.Base64Content))
                {
                    _logger.LogWarning("Empty base64 content for file {FileName}", attachment.FileName);
                    continue;
                }

                // Validate file type
                if (!IsAllowedFileType(attachment.ContentType))
                {
                    _logger.LogWarning("Invalid file type {ContentType} for file {FileName}",
                        attachment.ContentType, attachment.FileName);
                    continue;
                }

                // Decode the base64 string to a byte array
                byte[] fileBytes;
                try
                {
                    fileBytes = Convert.FromBase64String(attachment.Base64Content);
                }
                catch (FormatException ex)
                {
                    _logger.LogWarning(ex, "Invalid base64 string for file {FileName}", attachment.FileName);
                    continue;
                }

                // Create file upload DTO
                var fileUploadDto = new FileUploadDto
                {
                    Description = attachment.Description ?? $"Guest attachment for {guestName}",
                    ReferenceId = guestId,
                    ReferenceType = "Guest"
                };

                // Upload file
                await _attachmentAppService.UploadFileAsync(
                    fileUploadDto,
                    attachment.FileName,
                    attachment.ContentType,
                    fileBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing guest attachment {FileName}: {Message}",
                    attachment.FileName, ex.Message);
                // Continue with other attachments even if one fails
            }
        }
    }

    /// <summary>
    /// Checks if the file type is allowed (PDF or image)
    /// </summary>
    private static bool IsAllowedFileType(string contentType)
    {
        if (string.IsNullOrEmpty(contentType))
            return false;

        contentType = contentType.ToLower();

        // Allow PDF files
        if (contentType == "application/pdf")
            return true;

        // Allow image files
        if (contentType.StartsWith("image/"))
            return true;

        return false;
    }
}