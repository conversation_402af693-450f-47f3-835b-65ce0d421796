﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.PaymentGuests;

[Authorize(WismaAppPermissions.PolicyPaymentGuest.Default)]
public class PaymentGuestsAppService : IdentityServerCrudAppService<
    PaymentGuest,
    PaymentGuestsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdatePaymentGuestsDto,
    CreateUpdatePaymentGuestsDto
>, IPaymentGuestsAppService
{
    private readonly IRepository<PaymentGuest, Guid> _repository;

    public PaymentGuestsAppService(IRepository<PaymentGuest, Guid> repository,
        ILogger<PaymentGuestsAppService> logger)
        : base(repository, logger)
    {
        _repository = repository;
    }
}