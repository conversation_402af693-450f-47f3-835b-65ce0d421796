using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.PaymentGuests;

[Authorize(WismaAppPermissions.PolicyPaymentGuest.Default)]
public class PaymentGuestsAppService : CrudAppService<
    PaymentGuest,
    PaymentGuestsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdatePaymentGuestsDto,
    CreateUpdatePaymentGuestsDto
>, IPaymentGuestsAppService
{
    private readonly IRepository<PaymentGuest, Guid> _repository;

    public PaymentGuestsAppService(IRepository<PaymentGuest, Guid> repository,
        ILogger<PaymentGuestsAppService> logger)
        : base(repository)
    {
        _repository = repository;
    }
    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyPaymentGuest.View)]
    public override Task<PagedResultDto<PaymentGuestsDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }
}